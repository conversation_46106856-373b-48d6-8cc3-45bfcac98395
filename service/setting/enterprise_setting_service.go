// Package setting  define  enterprise setting
package setting

import (
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	CError "ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
)

type EnterpriseSettingService interface {
	FindEnterpriseSetting(ctx context.Context, productId, Type string) (*dto.EnterpriseSetting, error)
}
type enterpriseSettingService struct {
	enterpriseSettingCollection *mongo.Collection
}

func NewEnterpriseSettingService() EnterpriseSettingService {
	return &enterpriseSettingService{
		enterpriseSettingCollection: client.Mongo.Database.Collection("enterprise_setting"), // 企业设置表
	}
}

func (e *enterpriseSettingService) FindEnterpriseSetting(ctx context.Context, productId, Type string) (*dto.EnterpriseSetting, error) {
	setting := &dto.EnterpriseSetting{}
	if err := e.enterpriseSettingCollection.FindOne(ctx, bson.M{"product_id": productId, "type": Type}).Decode(&setting); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		logs.Warnf("根据产品id :%s 查询企业设置失败: %v", productId, err)
		return nil, CError.ErrorEnterpriseSetting
	}
	return setting, nil
}
