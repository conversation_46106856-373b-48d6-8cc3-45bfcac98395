package log_writer

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"sync"
	"time"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
)

type Service interface {
	WriteLog(log dto.LoginAccess)
}

type LogWriter struct {
	coll     *mongo.Collection
	aggColl  *mongo.Collection
	logsChan chan dto.LoginAccess
	wg       sync.WaitGroup
	quit     chan struct{}
}

// NewLogWriter 初始化一个批量日志写入器
func NewLogWriter(bufferSize int) *LogWriter {
	lw := &LogWriter{
		coll:     client.Mongo.Database.Collection("login_access_log"),
		aggColl:  client.Mongo.Database.Collection("login_access_agg"),
		logsChan: make(chan dto.LoginAccess, bufferSize),
		quit:     make(chan struct{}),
	}
	lw.start()
	return lw
}

func (lw *LogWriter) start() {
	lw.wg.Add(1)
	go func() {
		defer lw.wg.Done()

		ticker := time.NewTicker(1 * time.Second) // 每 1 秒 flush 一次
		defer ticker.Stop()

		batch := make([]*dto.LoginAccess, 0, 100) // 每批最多 100 条

		for {
			select {
			case log := <-lw.logsChan:
				batch = append(batch, &log)
				if len(batch) >= 100 { // 达到批量上限
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-ticker.C:
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-lw.quit:
				// flush 剩余数据
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
				return
			}
		}
	}()
}

func (lw *LogWriter) flush(batch []*dto.LoginAccess) {

	if len(batch) == 0 {
		return
	}

	var docs = make([]interface{}, 0, len(batch))
	for _, doc := range batch {
		docs = append(docs, doc)
	}

	if _, err := lw.coll.InsertMany(context.Background(), docs); err != nil {
		fmt.Println("batch insert log error:", err)
	} else {
		// 插入成功后，更新聚合统计
		lw.aggLoginAccess(batch)
	}
	batch = make([]*dto.LoginAccess, 0, 100)
}
func (lw *LogWriter) aggLoginAccess(batch []*dto.LoginAccess) {
	// 统计需要更新的聚合数据
	userCodeAggMap := make(map[string]*dto.LoginAccessAgg)
	clientIdAggMap := make(map[string]*dto.LoginAccessAgg)

	// 收集需要统计的维度
	for _, log := range batch {
		// Type=1: 按 userCode 统计不重复的 clientId
		userKey := fmt.Sprintf("%s_%s_%s", log.OrgCode, log.ProductID, log.UserCode)
		if _, exists := userCodeAggMap[userKey]; !exists {
			userCodeAggMap[userKey] = &dto.LoginAccessAgg{
				OrgCode:   log.OrgCode,
				OrgName:   log.OrgName,
				ProductID: log.ProductID,
				Type:      1,
				Object:    log.UserCode,
			}
		}

		// Type=2: 按 clientId 统计不重复的 userCode
		clientKey := fmt.Sprintf("%s_%s_%s", log.OrgCode, log.ProductID, log.ClientID)
		if _, exists := clientIdAggMap[clientKey]; !exists {
			clientIdAggMap[clientKey] = &dto.LoginAccessAgg{
				OrgCode:   log.OrgCode,
				OrgName:   log.OrgName,
				ProductID: log.ProductID,
				Type:      2,
				Object:    log.ClientID,
			}
		}
	}

	// 更新 userCode 聚合统计
	for _, agg := range userCodeAggMap {
		lw.updateUserCodeAgg(agg)
	}

	// 更新 clientId 聚合统计
	for _, agg := range clientIdAggMap {
		lw.updateClientIdAgg(agg)
	}
}
func (lw *LogWriter) WriteLog(log dto.LoginAccess) {
	select {
	case lw.logsChan <- log:
	default:
		// 如果队列满了，丢弃或打印（可改成写文件做兜底）
		fmt.Println("log channel full, dropping log:", log)
	}
}

func (lw *LogWriter) Stop() {
	close(lw.quit)
	lw.wg.Wait()
}

// updateUserCodeAgg 更新按 userCode 统计的聚合数据（统计不重复的 clientId 数量）
func (lw *LogWriter) updateUserCodeAgg(agg *dto.LoginAccessAgg) {
	ctx := context.Background()

	// 统计该 userCode 对应的不重复 clientId 数量
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"org_code":   agg.OrgCode,
				"product_id": agg.ProductID,
				"user_code":  agg.Object,
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"client_id": "$client_id",
				},
			},
		},
		{
			"$count": "distinct_count",
		},
	}

	cursor, err := lw.coll.Aggregate(ctx, pipeline)
	if err != nil {
		fmt.Printf("aggregate user code error: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var result []bson.M
	if err := cursor.All(ctx, &result); err != nil {
		fmt.Printf("decode aggregate result error: %v\n", err)
		return
	}

	var distinctCount int64 = 0
	if len(result) > 0 {
		if count, ok := result[0]["distinct_count"].(int32); ok {
			distinctCount = int64(count)
		}
	}

	// 更新聚合表
	filter := bson.M{
		"org_code":   agg.OrgCode,
		"product_id": agg.ProductID,
		"type":       1,
		"object":     agg.Object,
	}

	update := bson.M{
		"$set": bson.M{
			"agg_count":  distinctCount,
			"updated_at": time.Now().Unix(),
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now().Unix(),
			"org_name":   agg.OrgName,
		},
	}

	opts := options.Update().SetUpsert(true)
	if _, err := lw.aggColl.UpdateOne(ctx, filter, update, opts); err != nil {
		fmt.Printf("update user code agg error: %v\n", err)
	}
}

// updateClientIdAgg 更新按 clientId 统计的聚合数据（统计不重复的 userCode 数量）
func (lw *LogWriter) updateClientIdAgg(agg *dto.LoginAccessAgg) {
	ctx := context.Background()

	// 统计该 clientId 对应的不重复 userCode 数量
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"org_code":   agg.OrgCode,
				"product_id": agg.ProductID,
				"client_id":  agg.Object,
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"user_code": "$user_code",
				},
			},
		},
		{
			"$count": "distinct_count",
		},
	}

	cursor, err := lw.coll.Aggregate(ctx, pipeline)
	if err != nil {
		fmt.Printf("aggregate client id error: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var result []bson.M
	if err := cursor.All(ctx, &result); err != nil {
		fmt.Printf("decode aggregate result error: %v\n", err)
		return
	}

	var distinctCount int64 = 0
	if len(result) > 0 {
		if count, ok := result[0]["distinct_count"].(int32); ok {
			distinctCount = int64(count)
		}
	}

	// 更新聚合表
	filter := bson.M{
		"org_code":   agg.OrgCode,
		"product_id": agg.ProductID,
		"type":       2,
		"object":     agg.Object,
	}

	update := bson.M{
		"$set": bson.M{
			"agg_count":  distinctCount,
			"updated_at": time.Now().Unix(),
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now().Unix(),
			"org_name":   agg.OrgName,
		},
	}

	opts := options.Update().SetUpsert(true)
	if _, err := lw.aggColl.UpdateOne(ctx, filter, update, opts); err != nil {
		fmt.Printf("update client id agg error: %v\n", err)
	}
}
