package log_writer

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"sync"
	"time"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
)

type Service interface {
	WriteLog(log dto.LoginAccess)
}

type LogWriter struct {
	coll     *mongo.Collection
	aggColl  *mongo.Collection
	logsChan chan dto.LoginAccess
	wg       sync.WaitGroup
	quit     chan struct{}
}

// NewLogWriter 初始化一个批量日志写入器
func NewLogWriter(bufferSize int) *LogWriter {
	lw := &LogWriter{
		coll:     client.Mongo.Database.Collection("login_access_log"),
		aggColl:  client.Mongo.Database.Collection("login_access_agg"),
		logsChan: make(chan dto.LoginAccess, bufferSize),
		quit:     make(chan struct{}),
	}
	lw.start()
	return lw
}

func (lw *LogWriter) start() {
	lw.wg.Add(1)
	go func() {
		defer lw.wg.Done()

		ticker := time.NewTicker(1 * time.Second) // 每 1 秒 flush 一次
		defer ticker.Stop()

		batch := make([]*dto.LoginAccess, 0, 100) // 每批最多 100 条

		for {
			select {
			case log := <-lw.logsChan:
				batch = append(batch, &log)
				if len(batch) >= 100 { // 达到批量上限
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-ticker.C:
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-lw.quit:
				// flush 剩余数据
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
				return
			}
		}
	}()
}

func (lw *LogWriter) flush(batch []*dto.LoginAccess) {

	if len(batch) == 0 {
		return
	}

	var docs = make([]interface{}, 0, len(batch))
	for _, doc := range batch {
		docs = append(docs, doc)
	}

	if _, err := lw.coll.InsertMany(context.Background(), docs); err != nil {
		fmt.Println("batch insert log error:", err)
	} else {

	}
	batch = make([]*dto.LoginAccess, 0, 100)
}
func (lw *LogWriter) aggLoginAccess(batch []*dto.LoginAccess) {
	// group by org_code, product_id, user_code and count distinct client_id
	for _, log := range batch {
		filter := bson.M{
			"org_code":   log.OrgCode,
			"product_id": log.ProductID,
			"type":       1,
			"object":     log.UserCode,
		}
		update := bson.M{
			"$inc": bson.M{"agg_count": 1},
			"$set": bson.M{
				"org_name":   log.OrgName,
				"updated_at": time.Now().Unix(),
			},
			"$setOnInsert": bson.M{
				"created_at": time.Now().Unix(),
			},
		}
		opts := options.Update().SetUpsert(true)
		if _, err := lw.aggColl.UpdateOne(context.Background(), filter, update, opts); err != nil {
			fmt.Println("agg user login access error:", err)
		}
		filter = bson.M{
			"org_code":   log.OrgCode,
			"product_id": log.ProductID,
			"type":       2,
			"object":     log.ClientID,
		}
		update = bson.M{
			"$inc": bson.M{"agg_count": 1},
			"$set": bson.M{
				"updated_at": time.Now().Unix(),
			},
			"$setOnInsert": bson.M{
				"created_at": time.Now().Unix(),
			},
		}
		if _, err := lw.aggColl.UpdateOne(context.Background(), filter, update, opts); err != nil {
			fmt.Println("agg client login access error:", err)
		}
	}
}
func (lw *LogWriter) WriteLog(log dto.LoginAccess) {
	select {
	case lw.logsChan <- log:
	default:
		// 如果队列满了，丢弃或打印（可改成写文件做兜底）
		fmt.Println("log channel full, dropping log:", log)
	}
}

func (lw *LogWriter) Stop() {
	close(lw.quit)
	lw.wg.Wait()
}
