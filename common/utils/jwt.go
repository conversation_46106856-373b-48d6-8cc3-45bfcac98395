package utils

import (
	"errors"
	"fmt"
	"strings"
	"time"

	commondto "ztna_server_auth/common/dto"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"

	"github.com/golang-jwt/jwt/v5"
)

// GenerateJWT 生成JWT令牌
func GenerateJWT(claims *commondto.JWTClaims, secretKey string) (string, error) {
	fmt.Println("secretKey", secretKey)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	fmt.Println("token", token)
	return token.SignedString([]byte(secretKey))
}

// ParseJWT 解析JWT令牌
func ParseJWT(tokenString string, secretKey string) (*commondto.JWTClaims, error) {
	claims := &commondto.JWTClaims{}

	// 打印token的基本信息
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		logs.PErrorf("tokenString: %s 不符合JWT格式", tokenString)
		return nil, errors.New("invalid token")
	}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Method.Alg())
		}
		return []byte(secretKey), nil
	}, jwt.WithValidMethods([]string{"HS256"}))
	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			// 打印过期相关信息
			if claims.ExpiresAt != nil {
				logs.PErrorf("Token expired at: %v (%d)", claims.ExpiresAt.Time.Format(time.RFC3339), claims.ExpiresAt.Unix())
			}
			return nil, err
		}

		// 打印验证错误的详细信息
		fmt.Printf("JWT validation error: %v\n", err)
		fmt.Printf("Raw error type: %T\n", err)

		// 检查是否是签名错误
		if errors.Is(err, jwt.ErrSignatureInvalid) {
			fmt.Println("Invalid token signature")
		}

		// 检查是否是格式错误
		if errors.Is(err, jwt.ErrTokenMalformed) {
			fmt.Println("Malformed token")
		}

		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// 打印成功解析的claims信息
	fmt.Printf("Successfully parsed token for user: %s\n", claims.UserCode)
	fmt.Printf("Token type: %s\n", claims.TokenType)
	if claims.ExpiresAt != nil {
		fmt.Printf("Token expires at: %v\n", claims.ExpiresAt.Time.Format(time.RFC3339))
	}
	if claims.IssuedAt != nil {
		fmt.Printf("Token issued at: %v\n", claims.IssuedAt.Time.Format(time.RFC3339))
	}
	if claims.NotBefore != nil {
		fmt.Printf("Token valid from: %v\n", claims.NotBefore.Time.Format(time.RFC3339))
	}

	return claims, nil
}

// GenerateAccessTokenClaims 生成访问令牌声明
func GenerateAccessTokenClaims(user *dto.AuthUserInfo, partnerId string, expiry time.Duration, issuer string) commondto.JWTClaims {
	now := time.Now()
	// 添加1分钟的缓冲时间，避免因时间差导致的立即过期
	exp := now.Add(expiry).Add(1 * time.Minute)

	fmt.Printf("Token Generation Time Details:\n")
	fmt.Printf("Current time: %v (%d)\n", now.Format(time.RFC3339), now.Unix())
	fmt.Printf("Expiry time: %v (%d)\n", exp.Format(time.RFC3339), exp.Unix())
	fmt.Printf("Duration: %v\n", expiry)

	return commondto.JWTClaims{
		ProductId: user.ProductID,
		OrgCode:   user.OrgCode,
		UserCode:  user.UserCode,
		Username:  user.Username,
		Email:     user.Email,
		Roles:     []string{}, // 可以从用户信息中提取角色
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(exp),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    issuer,
			Subject:   user.UserCode,
			ID:        partnerId,
		},
	}
}

// GenerateRefreshTokenClaims 生成刷新令牌声明
func GenerateRefreshTokenClaims(user *dto.AuthUserInfo, partnerId string, expiry time.Duration, issuer string) commondto.JWTClaims {
	now := time.Now()
	// 添加1分钟的缓冲时间
	exp := now.Add(expiry).Add(1 * time.Minute)

	fmt.Printf("Refresh Token Generation Time Details:\n")
	fmt.Printf("Current time: %v (%d)\n", now.Format(time.RFC3339), now.Unix())
	fmt.Printf("Expiry time: %v (%d)\n", exp.Format(time.RFC3339), exp.Unix())
	fmt.Printf("Duration: %v\n", expiry)

	return commondto.JWTClaims{
		ProductId: user.ProductID,
		UserCode:  user.UserCode,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(exp),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    issuer,
			Subject:   user.UserCode,
			ID:        partnerId,
		},
	}
}
