package utils

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

func GetClientIp(ctx *gin.Context) string {
	clientIp := ctx.Request.Header.Get("X-Forwarded-For")
	for _, ip := range strings.Split(clientIp, ",") {
		ip = strings.TrimSpace(ip)
		if ip != "" && ip != "127.0.0.1" {
			clientIp = ip
			break
		}
	}

	if clientIp == "" {
		clientIp = ctx.Request.Header.Get("X-Real-IP")
	}

	if clientIp == "" {
		clientIp = ctx.ClientIP()
	}

	if clientIp == "::1" {
		clientIp = "127.0.0.1"
	}

	return clientIp
}

func CreateCaptcha(num int) (string, error) {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 64)

	if err != nil {
		return "", fmt.Errorf("生成验证码错误：%v", err)
	}

	j := int32(int10)
	return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j)), nil
}

func GetNowForZeroTime(i int) time.Time {
	l, _ := time.LoadLocation("Asia/Shanghai")
	last := time.Now().AddDate(0, 0, i).Unix()
	lastTimestemp := time.Unix(last, 0).Format("2006-01-02")
	lastZeroUnix, _ := time.ParseInLocation("2006-01-02", lastTimestemp, l)

	return lastZeroUnix
}

func Md5Str(source string) string {
	hash := md5.Sum([]byte(source)) // 返回 [16]byte

	// 转换为十六进制字符串
	return hex.EncodeToString(hash[:])

}

func GenerateOrgCode() string {
	orgCode := Md5Str(uuid.New().String())
	return fmt.Sprintf("ORG_%s", orgCode)
}

func GenerateUserCode() string {
	return fmt.Sprintf("USER_%s", Md5Str(uuid.New().String()))
}

func Base64Decode(str string) (string, error) {
	decodeBytes, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return "", err
	}

	return string(decodeBytes), nil
}
