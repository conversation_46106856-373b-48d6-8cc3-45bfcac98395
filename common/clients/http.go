package clients

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
	"ztna_server_auth/common/config"
	"ztna_server_auth/dto"
)

func SendLoginLog(data *dto.LoginAccess) error {

	dataBytes, _ := json.Marshal(data)
	println("dataBytes is %s", string(dataBytes))

	req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/log-tracker/v1/identity/logs", config.Config().ThirdParty.LogTrackerAddress), bytes.NewBuffer(dataBytes))
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	return err
}
