package clients

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
	"time"
	"ztna_server_auth/common/config"
	"ztna_server_auth/dto"
)

func SendLoginLog(data *dto.LoginAccess) error {

	reader, writer := io.Pipe()

	dataBytes, _ := json.Marshal(data)

	if _, err := io.WriteString(writer, fmt.Sprintf(string(dataBytes)+"\n")); err != nil {
		log.Error("write to pipe error: %v", err)
		return err
	}

	req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/log-tracker/v1/indicator_report", config.Config().ThirdParty.LogTrackerAddress), reader)
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.<PERSON>.Close()
	return err
}
