package errors

var (
	InvalidParameter          = New(10000, "invalid parameter", "参数错误")
	InvalidToken              = New(10001, "invalid token or token expired", "token错误或已过期")
	LogoutFailed              = New(10002, "logout failed", "注销失败")
	LogoutSuccess             = New(10003, "logout success", "注销成功")
	GetOrgListFailed          = New(10004, "get org list failed", "获取组织列表失败")
	GetAppListFailed          = New(10005, "get app list failed", "获取应用列表失败")
	Unauthorized              = New(10007, "unauthorized", "未授权")
	InvalidRefreshToken       = New(10008, "invalid refresh token", "刷新token错误")
	ThirdPartyLoginFailed     = New(10009, "third party login failed", "第三方登录失败")
	ErrorOfCaptcha            = New(10010, "Captcha error", "验证码错误")
	ImageCaptchaError         = New(10011, "Image captcha error", "验证码错误")
	ErrVerificationCodeCreate = New(10012, "The generation of the verification code is error.", "验证码生成失败") //
	ErrSmsTimeDay             = New(10013, "Please try again later.", "请稍后重试")
	ErrSmsTimeDayForIp        = New(10014, "Please try again later.", "请稍后重试")
	ErrVerificationCodePhone  = New(10015, "Incorrect phone number.", "账户格式错误")
	ErrSendVerificationCode   = New(10016, "You cannot request the verification code again within two minutes.", "两分钟内不你能重复请求验证码")
	ErrInternalServer         = New(10017, "Please try again later.", "请稍后重试")
	ErrorPassword             = New(10018, "Incorrect username or password.", "用户名或密码错误")
	ResetPwdFailed            = New(10019, "Reset password failed", "重置密码失败")
	ErrorUserBlocked          = New(10020, "User blocked", "用户被暂停使用")
	ErrorUserNotFound         = New(10021, "User not found", "用户不存在")
	ErrorResetPassword        = New(10022, "The new password cannot be the same as the old password", "新密码不能与旧密码相同")
	ErrSmsSend                = New(10023, "Please try again later.", "请稍后重试")
	ErrorEnterpriseSetting    = New(10025, "Enterprise setting Find error", "查询企业设置错误")
	IpBannedError             = New(10026, "This IP has been temporarily banned due to multiple login failures. Please try again in% d minutes", "该IP因多次登录失败已被临时封禁,请 %d 分钟后重试")
	AccountBannedError        = New(10027, "This account has been temporarily banned due to multiple login failures. Please try again in% d minutes", "该账户因多次登录失败已被临时封禁,请 %d 分钟后重试")
	ErrorSelfChangePwd        = New(10029, "Not allowed to change password by oneself, please contact the administrator", "不允许自己修改密码,请联系管理员")
	ErrorPasswordSpecial      = New(10031, "Password contains special characters", "密码需包含特殊字符")
	ErrorPasswordLength       = New(10032, "Password length is too short", "密码长度不够")
	ErrorPasswordNumber       = New(10033, "Password must contain numbers", "密码需包含数字")
	ErrorPasswordUpper        = New(10034, "Password must contain uppercase letters", "密码需包含大写字母")
	ErrorPasswordLower        = New(10035, "Password must contain lowercase letters", "密码需包含小写字母")
	ErrorUserLocked           = New(10036, "User locked", "用户被锁定")
	ErrorGetOrganization      = New(10037, "The organization does not exist", "当前组织不存在")
	ErrorGuestLogin           = New(10038, "Guest login failed", "访客登录失败")
	ErrorOrgDomainNotExist    = New(10039, "The current user is not within the domain of this organization and cannot log in", "当前用户不在该组织的域内，无法登录")
)
