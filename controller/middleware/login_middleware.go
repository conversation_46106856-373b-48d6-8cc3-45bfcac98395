package middleware

import (
	"github.com/gin-gonic/gin"
	"time"
	"ztna_server_auth/common/clients"
	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/service/log_writer"
)

func LoginMiddleware(service log_writer.Service) gin.HandlerFunc {

	return func(c *gin.Context) {

		var (
			userCode      string
			userName      string
			clientId      string
			orgCode       string
			userLoginTime int64
			platform      string
			clientIp      string
		)
		userLoginTime = time.Now().Unix()
		clientId = c.GetHeader(consts.ClientID)
		clientIp = utils.GetClientIp(c)
		platform = "Web" // 浏览器

		c.Next()

		accessLog := dto.LoginAccess{
			Action:     "Login",
			OrgCode:    c.GetString(consts.OrgCode),
			OrgName:    c.GetString(consts.OrgName),
			ProductID:  c.GetString(consts.ProductID),
			ClientID:   c.GetHeader(consts.ClientID),
			IP:         utils.GetClientIp(c),
			UserCode:   c.GetString(consts.UserCode),
			UserName:   c.GetString(consts.UserName),
			LoginTime:  userLoginTime,
			Platform:   "Web",
			CreateTime: time.Now().Unix()}

		logs.Info("userCode: %s, userName: %s, orgCode: %s, userLoginTime: %d, clientId: %s, clientIp: %s, platform: %s",
			userCode, userName, orgCode, userLoginTime, clientId, clientIp, platform)
		if accessLog.ClientID == "" {
			accessLog.ClientID = c.GetString(consts.ClientID)
		}

		service.WriteLog(accessLog)
		// 日志上报的数据
		clients.SendLoginLog(&accessLog)

	}
}
