package auth

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"ztna_server_auth/common"
	"ztna_server_auth/common/config"
	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
	"ztna_server_auth/service"
)

var conf = config.Config()

// Controller  处理认证相关的HTTP请求
type Controller struct{}

// NewAuthController 创建新的认证控制器
func NewAuthController() *Controller {
	return &Controller{}
}

// HandleAuthorize 处理OAuth2.0授权请求
func (c *Controller) HandleAuthorize(ctx *gin.Context) {
	// 解析授权请求参数
	request := dto.OAuthAuthorizeRequest{
		ResponseType: ctx.Query("response_type"),
		ClientID:     ctx.Query("client_id"),
		RedirectURI:  ctx.Query("redirect_uri"),
		Scope:        ctx.Query("scope"),
		State:        ctx.Query("state"),
	}

	// 验证必要参数
	if request.ResponseType == "" || request.ClientID == "" || request.RedirectURI == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters"})
		return
	}

	// 验证response_type是否为code或token
	if request.ResponseType != "code" && request.ResponseType != "token" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid response_type"})
		return
	}

	// 在实际应用中，这里应该重定向到登录页面或授权页面
	// 本示例仅返回一个简单的HTML表单作为示例
	html := `
	<!DOCTYPE html>
	<html>
	<head>
		<title>授权请求</title>
	</head>
	<body>
		<h1>授权请求</h1>
		<p>应用 ClientID: ` + request.ClientID + ` 请求授权</p>
		<p>请求权限: ` + request.Scope + `</p>
		<form method="post" action="/oauth/authorize">
			<input type="hidden" name="client_id" value="` + request.ClientID + `">
			<input type="hidden" name="redirect_uri" value="` + request.RedirectURI + `">
			<input type="hidden" name="state" value="` + request.State + `">
			<input type="hidden" name="response_type" value="` + request.ResponseType + `">
			<input type="hidden" name="scope" value="` + request.Scope + `">
			<button type="submit" name="action" value="allow">授权</button>
			<button type="submit" name="action" value="deny">拒绝</button>
		</form>
	</body>
	</html>
	`
	ctx.Header("Content-Type", "text/html")
	ctx.String(http.StatusOK, html)
}

// HandleToken 处理OAuth2.0令牌请求
func (c *Controller) HandleToken(ctx *gin.Context) {
	var request dto.OAuthTokenRequest
	if err := ctx.ShouldBind(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// 验证grant_type
	var tokenResponse *dto.TokenResponse
	var err error

	switch request.GrantType {
	case "authorization_code":
		// 授权码模式
		if request.Code == "" || request.RedirectURI == "" || request.ClientID == "" || request.ClientSecret == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters for authorization_code grant"})
			return
		}
		ctx.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented"})
		return

	case "password":
		// 密码模式
		if request.Username == "" || request.Password == "" || request.ClientID == "" || request.ClientSecret == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters for password grant"})
			return
		}
		tokenResponse, err = service.AuthService.Login(ctx.Request.Context(), &dto.GetUserInfoByPhoneOrEmail{
			Pwd:     request.Password,
			Account: request.Username,
		})
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
			return
		}

	case "client_credentials":
		// 客户端凭证模式
		if request.ClientID == "" || request.ClientSecret == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters for client_credentials grant"})
			return
		}
		ctx.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented"})
		return

	case "refresh_token":
		// 刷新令牌
		if request.RefreshToken == "" || request.ClientID == "" || request.ClientSecret == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters for refresh_token grant"})
			return
		}
		tokenResponse, err = service.AuthService.RefreshToken(ctx.Request.Context(), request.RefreshToken)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
			return
		}

	default:
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported grant_type"})
		return
	}

	ctx.JSON(http.StatusOK, tokenResponse)
}

// HandleLogin 处理登录请求
func (c *Controller) HandleLogin(ctx *gin.Context) {
	var request dto.LoginRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}
	if err := c.IPAndAccountBanCheck(ctx, &dto.LoginAccountIP{
		Account:   request.Account,
		ProductID: request.ProductID,
		OrgCode:   request.OrgCode,
	}); err != nil {
		common.ResponseResult(ctx, err)
		return
	}
	if err := utils.VerifyImageCode(ctx, request.Ticket, request.RandStr); err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   request.Account,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}

	// 验证用户名和密码
	tokenResponse, err := service.AuthService.Login(ctx.Request.Context(), &dto.GetUserInfoByPhoneOrEmail{
		OrgCode:   request.OrgCode,
		Account:   request.Account,
		Pwd:       request.Password,
		ProductID: request.ProductID,
		Ticket:    ctx.GetHeader(consts.RMTicket),
	})
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   request.Account,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, err)
		return
	}

	clearLoginFail(ctx, &dto.LoginAccountIP{
		Account:   request.Account,
		ProductID: request.ProductID,
	})
	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = consts.AccountPassword

	SetUserLoginInfoToContext(ctx, tokenResponse)

	common.ResponseResult(ctx, tokenResponse)
}

// HandleLogout 处理登出请求
func (c *Controller) HandleLogout(ctx *gin.Context) {
	// 从请求头获取令牌
	authHeader := ctx.GetHeader("Authorization")
	if authHeader == "" {
		logs.PErrorf("handleLogout: No authorization header provided")
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 解析令牌
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		logs.PErrorf("handleLogout: Invalid authorization header format")
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	accessToken := parts[1]

	// 登出
	err := service.AuthService.Logout(ctx.Request.Context(), accessToken)
	if err != nil {
		logs.PErrorf("登出失败: %v", err)
		common.ResponseResult(ctx, errors.LogoutFailed)
		return
	}

	// 返回成功响应
	common.ResponseResult(ctx, errors.LogoutSuccess)
}

// HandleRefreshToken 处理刷新令牌请求
func (c *Controller) HandleRefreshToken(ctx *gin.Context) {
	var request dto.RefreshTokenRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 刷新令牌
	tokenResponse, err := service.AuthService.RefreshToken(ctx.Request.Context(), request.RefreshToken)
	if err != nil {
		common.ResponseResult(ctx, errors.InvalidRefreshToken)
		return
	}

	// 返回新令牌
	ctx.JSON(http.StatusOK, tokenResponse)
}

// HandleThirdPartyLogin 处理第三方登录请求
func (c *Controller) HandleThirdPartyLogin(ctx *gin.Context) {
	var request dto.ThirdPartyLoginRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 验证提供商和授权码
	if request.Provider == "" || request.Code == "" {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 进行第三方登录
	tokenResponse, err := service.AuthService.ThirdPartyLogin(ctx.Request.Context(), request.Provider, "", request.Code, ctx.GetHeader(consts.RMTicket))
	if err != nil {
		logs.PErrorf("第三方登录失败: %v", err)
		common.ResponseResult(ctx, errors.ThirdPartyLoginFailed)
		return
	}
	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = request.Provider

	// 返回令牌
	ctx.JSON(http.StatusOK, tokenResponse)
}

// HandleDingTalkLogin 处理钉钉回调
func (c *Controller) HandleDingTalkLogin(ctx *gin.Context) {
	// 获取url参数
	query := ctx.Request.URL.Query()
	orgCode := query.Get("org_code") // 企业ID
	state := query.Get("state")
	authCode := query.Get("auth_code")

	logs.PInfof("handleDingTalkLogin: orgCode=%s, authCode=%s, state=%s", orgCode, authCode, state)
	// 验证code
	if orgCode == "" || authCode == "" {
		logs.PErrorf("handleDingTalkLogin: Missing required parameters: orgCode=%s, authCode=%s", orgCode, authCode)
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 根据authCode获取用户信息
	tokenResponse, err := service.AuthService.ThirdPartyLogin(ctx.Request.Context(), consts.Dingtalk, orgCode, authCode, state)
	if err != nil {
		logs.PErrorf("handleDingTalkLogin: Third-party login failed: %v", err)
		common.ResponseResult(ctx, errors.ThirdPartyLoginFailed)
		return
	}
	var bussiness = &dto.BussinessDataOnDingDing{}
	decodeBytes, err := base64.StdEncoding.DecodeString(state)
	if err == nil {
		err = json.Unmarshal(decodeBytes, bussiness)
		if err != nil {
			logs.PErrorf("handleDingTalkLogin: Unmarshal bussiness failed: %v", err)
		}
	}

	tokenResponse.Ticket = bussiness.Ticket
	tokenResponse.LoginType = consts.Dingtalk

	ctx.Set(consts.ClientID, bussiness.ClientID)

	SetUserLoginInfoToContext(ctx, tokenResponse)
	// 返回令牌
	common.ResponseResult(ctx, tokenResponse)
}

// ValidateTokenEndpoint 处理token验证请求
func (c *Controller) ValidateTokenEndpoint(ctx *gin.Context) {
	// 从请求头获取token
	userInfo, err := GetUserInfo(ctx)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	// 返回用户信息
	ctx.JSON(http.StatusOK, dto.TokenValidationResponse{
		Status: "success",
		User:   userInfo,
	})
}

// HandlePhoneLogin 处理手机号验证码登录
func (c *Controller) HandlePhoneLogin(ctx *gin.Context) {
	var request dto.PhoneLoginRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if err := c.IPAndAccountBanCheck(ctx, &dto.LoginAccountIP{
		Account:   request.Phone,
		ProductID: request.ProductID,
		OrgCode:   request.OrgCode,
	}); err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	redisKey := consts.GetSendMessageCodeRedisKey(string(dto.Login), request.Phone)

	cacheCode, err := client.Redis.Get(ctx, redisKey).Result()
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   request.Phone,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}
	// 验证验证码
	if request.Code != cacheCode {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   request.Phone,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}

	// 生成token
	tokenResponse, err := service.AuthService.PhoneLogin(ctx, request.Phone, request.OrgCode, request.ProductID, ctx.GetHeader(consts.RMTicket))
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   request.Phone,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, err)
		return
	}

	clearLoginFail(ctx, &dto.LoginAccountIP{
		Account:   request.Phone,
		ProductID: request.ProductID,
	})

	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = consts.PhoneLogin
	SetUserLoginInfoToContext(ctx, tokenResponse)
	common.ResponseResult(ctx, tokenResponse)
}

// HandleOrgList 根据productId获取组织列表
func (c *Controller) HandleOrgList(ctx *gin.Context) {
	var (
		portalConfig = &dto.PortalConfig{}
		err          error
		request      dto.OrgListRequest
	)
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if request.ProductID == "" && request.ProductID == "" {
		logs.Warnf("request data is empty")
		common.ResponseResult(ctx, errors.InvalidParameter)
		return

	}

	if request.SubDomain != "" {
		portalConfig, err = service.PortalConfigService.FindPortalConfigBySubDomain(ctx, request.SubDomain)
		if err != nil {
			common.ResponseResult(ctx, errors.GetOrgListFailed)
			return
		}
	}

	if portalConfig.ProductID != "" {
		request.ProductID = portalConfig.ProductID
	}

	// 根据productId获取组织列表
	orgList, err := service.AuthService.GetOrgList(ctx.Request.Context(), request.ProductID)
	if err != nil {
		common.ResponseResult(ctx, errors.GetOrgListFailed)
		return
	}

	common.ResponseResult(ctx, orgList)
}

// HandleOrgs 根据productId获取组织列表
func (c *Controller) HandleOrgs(ctx *gin.Context) {
	var (
		portalConfig = &dto.PortalConfig{}
		err          error
		request      dto.OrgListRequest
	)
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if request.ProductID == "" && request.ProductID == "" {
		logs.Warnf("request data is empty")
		common.ResponseResult(ctx, errors.InvalidParameter)
		return

	}

	if request.SubDomain != "" {
		portalConfig, err = service.PortalConfigService.FindPortalConfigBySubDomain(ctx, request.SubDomain)
		if err != nil {
			common.ResponseResult(ctx, errors.GetOrgListFailed)
			return
		}
	}

	if portalConfig.ProductID != "" {
		request.ProductID = portalConfig.ProductID
	}

	// 根据productId获取组织列表
	orgList, err := service.AuthService.GetOrgList(ctx.Request.Context(), request.ProductID)
	if err != nil {
		common.ResponseResult(ctx, errors.GetOrgListFailed)
		return
	}
	var loginType = consts.LoginTypeOfNormal
	settings, err := service.EnterpriseSettingService.FindEnterpriseSetting(ctx, request.ProductID, "ogin_configuration")
	if err != nil {
		logs.Warnf("failed to call FindEnterpriseSetting error: %s", err)
	} else if settings.GetLoginSetting() != nil {
		loginType = settings.GetLoginSetting().LoginType
	}

	common.ResponseResult(ctx, &dto.OrgListResponseData{Items: orgList, LoginType: loginType})
}

func (c *Controller) HandleSendCaptcha(ctx *gin.Context) {
	var req dto.CaptchaRequest
	var err error

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if !conf.Service.IsTestMode {
		// 验证图形验证码
		if err := utils.VerifyImageCode(ctx, req.Ticket, req.RandStr); err != nil {
			common.ResponseResult(ctx, errors.ImageCaptchaError)
			return
		}
	}

	redisKey := consts.GetSendMessageCodeRedisKey(req.Source, req.Phone)
	redisKeyValidTime := consts.GetValidatedTimeKey(req.Source, redisKey)
	redisTimesDayForPhoneKey := consts.GetTimeDayForPhoneKey(req.Source, redisKey)
	redisTimesDayForIPKey := consts.GetSendMessageCodeRedisKeyForIp(req.Source, strings.ReplaceAll(utils.GetClientIp(ctx), ".", "_"))

	redisTimesDayForPhone, _ := client.Redis.Get(ctx, redisTimesDayForPhoneKey).Result()
	redisTimesDayForPhoneNum, _ := strconv.ParseInt(redisTimesDayForPhone, 10, 64)

	redisTimesDayForIP, _ := client.Redis.Get(ctx, redisTimesDayForIPKey).Result()
	redisTimesDayForIPNum, _ := strconv.ParseInt(redisTimesDayForIP, 10, 64)

	if redisTimesDayForPhoneNum >= int64(conf.Sms.VerificationCodeTimesDayForPhone) {
		common.ResponseResult(ctx, errors.ErrSmsTimeDay)
		return
	}

	if redisTimesDayForIPNum >= int64(conf.Sms.VerificationCodeTimesDayForIp) {
		common.ResponseResult(ctx, errors.ErrSmsTimeDayForIp)
		return
	}

	var captcha string
	if conf.Service.IsTestMode {
		captcha = "123456" // 测试模式下，验证码为123456
	} else {
		captcha, err = utils.CreateCaptcha(6)
		if err != nil {
			common.ResponseResult(ctx, errors.ErrVerificationCodeCreate)
			return
		}
	}

	regBool, err := regexp.MatchString(consts.Regs["phone"], req.Phone)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	if !regBool {
		common.ResponseResult(ctx, errors.ErrVerificationCodePhone)
		return
	}

	redisVal, _ := client.Redis.Get(ctx, redisKey).Result()

	if redisVal != "" {
		common.ResponseResult(ctx, errors.ErrSendVerificationCode)
		return
	}

	phoneNum, err := client.Redis.Incr(ctx, redisTimesDayForPhoneKey).Result()
	logs.Infof("set redis times phone key: %s val:%v error:%v", redisTimesDayForPhoneKey, phoneNum, err)

	ipNum, err := client.Redis.Incr(ctx, redisTimesDayForIPKey).Result()
	logs.Infof("set redis times ip key: %s val:%v error:%v", redisTimesDayForIPKey, ipNum, err)

	if phoneNum == 1 {
		ok, err := client.Redis.ExpireAt(ctx, redisTimesDayForPhoneKey, utils.GetNowForZeroTime(1)).Result()
		logs.Infof("set redis expire time phone key: %s res:%v error:%v", redisTimesDayForPhoneKey, ok, err)
	}

	if ipNum == 1 {
		ok, err := client.Redis.ExpireAt(ctx, redisTimesDayForIPKey, utils.GetNowForZeroTime(1)).Result()
		logs.Infof("set redis expire time ip key: %s res:%v error:%v", redisTimesDayForIPKey, ok, err)
	}

	_, err = client.Redis.Set(ctx, redisKey, captcha, time.Duration(conf.Sms.VerificationCodeInterval)*time.Second).Result()
	if err != nil {
		logs.Errorf("sms set key1: %s error:%v", redisKey, err)
		common.ResponseResult(ctx, errors.ErrInternalServer)
		return
	}

	_, err = client.Redis.Set(ctx, redisKeyValidTime, captcha, time.Duration(conf.Sms.VerificationCodeValidTime)*time.Minute).Result()
	if err != nil {
		logs.Errorf("sms set key2: %s error:%v", redisKeyValidTime, err)
		common.ResponseResult(ctx, errors.ErrInternalServer)
		return
	}
	// 发送短信验证码
	err = utils.SendMessage(req.Phone, consts.SmsTempVerificationCode, captcha)
	if err != nil {
		logs.Errorf("sms send code: %s:%s error:%v", req.Phone, captcha, err)
		common.ResponseResult(ctx, errors.ErrSmsSend)
		return
	}

	common.ResponseResult(ctx, struct{}{})
}

func (c *Controller) HandleResetPwd(ctx *gin.Context) {
	request := &dto.ResetPwdReq{}

	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}
	if err := c.checkPwd(ctx, request); err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	redisKey := consts.GetSendMessageCodeRedisKey(string(dto.ResetPassword), request.Phone)
	cacheCode, err := client.Redis.Get(ctx, redisKey).Result()
	if err != nil {
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}
	// 验证验证码
	if request.Code != cacheCode {
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}

	if err := service.AuthService.ResetPassword(ctx.Request.Context(), request); err != nil {
		logs.Errorf("reset password failed: %v", err)
		common.ResponseResult(ctx, err)
		return
	}
	common.ResponseResult(ctx, struct{}{})
}

func (c *Controller) HandleLoginResetPwd(ctx *gin.Context) {
	// 从请求头获取token

	userInfo, err := GetUserInfo(ctx)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	request := &dto.LoginResetPwd{}
	if err := ctx.ShouldBind(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if err := c.checkPwd(ctx, &dto.ResetPwdReq{
		Password:  request.Password,
		OrgCode:   userInfo.OrgCode,
		ProductID: userInfo.ProductID,
	}); err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	if err := service.AuthService.LoginResetPwd(ctx, userInfo.UserCode, &dto.ResetPwdReq{
		Password:  request.Password,
		OrgCode:   userInfo.OrgCode,
		ProductID: userInfo.ProductID,
	}); err != nil {
		common.ResponseResult(ctx, err)
		return
	}
	common.ResponseResult(ctx, "success")
}

func (c *Controller) HandleCheckToken(ctx *gin.Context) {

	expireTime, err := service.AuthService.GetTokenExpireInfo(ctx.Request.Context(), &dto.BindHostInfoDataRequest{
		ClientId: ctx.Query(consts.ClientID),
		Token:    GetAccessTokenFromCtx(ctx),
	})
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	common.ResponseResult(ctx, expireTime)
}

func (c *Controller) HandleLdapLogin(ctx *gin.Context) {
	req := &dto.LdapLoginRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}
	if err := c.IPAndAccountBanCheck(ctx, &dto.LoginAccountIP{
		Account:   req.Account,
		ProductID: req.ProductID,
		OrgCode:   req.OrgCode,
	}); err != nil {
		logs.PErrorf("ip oraccount ban error: %s", err)
		common.ResponseResult(ctx, err)
		return
	}

	tokenResponse, err := service.AuthService.LdapLogin(ctx, req)
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{Account: req.Account, ProductID: req.ProductID})
		common.ResponseResult(ctx, err)
		return
	}
	clearLoginFail(ctx, &dto.LoginAccountIP{
		Account:   req.Account,
		ProductID: req.ProductID,
	})

	tokenResponse.LoginType = consts.Ldap
	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	SetUserLoginInfoToContext(ctx, tokenResponse)
	common.ResponseResult(ctx, tokenResponse)
}

// HandleAuthenticate 验证是否需要验证
func (c *Controller) HandleAuthenticate(ctx *gin.Context) {
	req := &dto.AuthenticateRequest{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}
	needAuth, err := service.AuthService.Authenticate(ctx, req)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	common.ResponseResult(ctx, map[string]interface{}{
		"status": needAuth,
	})
}

// 登录失败处理
func (c *Controller) handleLoginFail(ctx *gin.Context, req *dto.LoginAccountIP) {
	entSet, err := service.EnterpriseSettingService.FindEnterpriseSetting(ctx, req.ProductID, "account_security")
	if err != nil || entSet == nil {
		logs.Errorf("根据产品id: %s 查询企业设置失败: %v", req.ProductID, err)
		return
	}
	setting := entSet.GetAccountSetting()
	// IP防护
	if setting.BruteForceProtection.IPLoginSwitch {
		ipKey := fmt.Sprintf("%s%s:%s", consts.IPLoginFailedPrefix, req.ProductID, utils.GetClientIp(ctx))
		count, _ := client.Redis.Incr(ctx, ipKey).Result()
		client.Redis.Expire(ctx, ipKey, time.Duration(setting.BruteForceProtection.IPLoginConfig.Duration)*time.Minute)
		if int(count) >= setting.BruteForceProtection.IPLoginConfig.FailedNum {
			banKey := fmt.Sprintf("%s%s:%s", consts.IPBanPrefix, req.ProductID, utils.GetClientIp(ctx))
			client.Redis.Set(ctx, banKey, 1, time.Duration(setting.BruteForceProtection.IPLoginConfig.ProhibitIPDuration)*time.Minute)
		}
	}
	// 账号防护
	if setting.BruteForceProtection.AccountLoginSwitch {
		accKey := fmt.Sprintf("%s%s:%s", consts.AccountLoginFailedPrefix, req.ProductID, req.Account)
		count, _ := client.Redis.Incr(ctx, accKey).Result()
		client.Redis.Expire(ctx, accKey, time.Duration(setting.BruteForceProtection.AccountLoginConfig.Duration)*time.Minute)
		if int(count) >= setting.BruteForceProtection.AccountLoginConfig.FailedNum {
			banKey := fmt.Sprintf("%s%s:%s", consts.AccountBanPrefix, req.ProductID, req.Account)
			client.Redis.Set(ctx, banKey, 1, time.Duration(setting.BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration)*time.Minute)
		}
	}
}

// 登录成功清理
func clearLoginFail(ctx *gin.Context, req *dto.LoginAccountIP) {
	client.Redis.Del(ctx, fmt.Sprintf("%s%s:%s", consts.IPLoginFailedPrefix, req.ProductID, utils.GetClientIp(ctx)))
	client.Redis.Del(ctx, fmt.Sprintf("%s%s:%s", consts.AccountLoginFailedPrefix, req.ProductID, req.Account))
}

// 检查IP是否被封禁
func isIPBanned(ctx context.Context, req *dto.LoginAccountIP, setting *dto.AccountSetting) bool {
	if !setting.BruteForceProtection.IPLoginSwitch {
		return false
	}
	key := fmt.Sprintf("%s%s:%s", consts.IPBanPrefix, req.ProductID, req.IP)
	res, err := client.Redis.Exists(ctx, key).Result()
	logs.Infof("get ipbanned key: %s res:%v error:%v", key, res, err)
	return res == 1
}

// 检查账号是否被封禁
func isAccountBanned(ctx context.Context, req *dto.LoginAccountIP, setting *dto.AccountSetting) bool {
	if !setting.BruteForceProtection.AccountLoginSwitch {
		return false
	}
	key := fmt.Sprintf("%s%s:%s", consts.AccountBanPrefix, req.ProductID, req.Account)

	res, err := client.Redis.Exists(ctx, key).Result()
	logs.Infof("get accountban key: %s res:%v error:%v", key, res, err)
	return res == 1
}

func (c *Controller) IPAndAccountBanCheck(ctx *gin.Context, req *dto.LoginAccountIP) error {
	entSet, err := service.EnterpriseSettingService.FindEnterpriseSetting(ctx, req.ProductID, "account_security")
	if err != nil || entSet == nil {
		logs.Errorf("根据产品id: %s 查询企业设置失败: %v", req.ProductID, err)
		return nil
	}
	req.IP = utils.GetClientIp(ctx)
	if isAccountBanned(ctx, req, entSet.GetAccountSetting()) {
		logs.Infof("product_id: %s, account: %s, ip: %s,account is banned", req.ProductID, req.Account, req.IP)
		_ = service.AuthService.SetUserInfoStatusLocked(ctx, req)
		return errors.New(errors.AccountBannedError.Code, fmt.Sprintf(errors.AccountBannedError.MessageEn, entSet.GetAccountSetting().BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration),
			fmt.Sprintf(errors.AccountBannedError.Message, entSet.GetAccountSetting().BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration))
	}

	if isIPBanned(ctx, req, entSet.GetAccountSetting()) {
		logs.Infof("product_id: %s, account: %s, ip: %s,ip is banned", req.ProductID, req.Account, req.IP)
		_ = service.AuthService.SetUserInfoStatusLocked(ctx, req)

		return errors.New(errors.IpBannedError.Code, fmt.Sprintf(errors.IpBannedError.MessageEn, entSet.GetAccountSetting().BruteForceProtection.IPLoginConfig.ProhibitIPDuration),
			fmt.Sprintf(errors.IpBannedError.Message, entSet.GetAccountSetting().BruteForceProtection.IPLoginConfig.ProhibitIPDuration))
	}

	return nil
}

func (c *Controller) checkPwd(ctx context.Context, req *dto.ResetPwdReq) error {
	return service.AuthService.ValidatePassword(ctx, &dto.ResetPwdReq{
		Password:  req.Password,
		OrgCode:   req.OrgCode,
		ProductID: req.ProductID,
	})
}

func (c *Controller) HandleLdapDomainAccountLogin(ctx *gin.Context) {
	req := &dto.LdapDomainAccountLoginRequest{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	req.Ticket = ctx.GetHeader(consts.RMTicket)

	tokenResponse, err := service.AuthService.LdapDomainAccountLogin(ctx, req)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = consts.LdapDomainAccount
	SetUserLoginInfoToContext(ctx, tokenResponse)
	common.ResponseResult(ctx, tokenResponse)
}

func (c *Controller) HandleGuestLogin(ctx *gin.Context) {
	req := &dto.GuestLoginRequest{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	req.Ticket = ctx.GetHeader(consts.RMTicket)
	// 先判断当前的用户的组织表里面有没有 然后创建根目录部门

	tokenResponse, err := service.AuthService.GuestLogin(ctx, req)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	tokenResponse.LoginType = consts.Guest
	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	SetUserLoginInfoToContext(ctx, tokenResponse)
	common.ResponseResult(ctx, tokenResponse)
}

func (c *Controller) HandleLoginList(ctx *gin.Context) {

	var req = &dto.LoginTypeListRequest{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}
	req.OrgCode = ctx.Param("org_code")

	data, err := service.AuthService.GetLoginTypeList(ctx, req)
	if err != nil {
		logs.Warnf("get login type list err: %v", err)
		common.ResponseResult(ctx, err)
		return
	}
	common.ResponseResult(ctx, data)
}
