package dto

type Organization struct {
	AppKey       string      `json:"app_key" bson:"app_key"`
	Secret       string      `json:"secret" bson:"secret"`
	OrgCode      string      `json:"org_code" bson:"org_code"`
	OrgName      string      `json:"org_name" bson:"org_name"`
	SourceType   string      `json:"source_type" bson:"source_type"`
	LdapConfig   *LdapConfig `json:"ldap_config" bson:"ldap_config"`
	SourceConfig *Config     `json:"source_config" bson:"source_config"`
	ProductID    string      `json:"product_id" bson:"product_id"`
	SSOConfig    *SSOConfig  `json:"sso_config" bson:"sso_config"`
}

type SSOConfig struct {
	SSOCode  string `json:"sso_code" bson:"sso_code"`
	InnerNet bool   `json:"inner_net" bson:"inner_net"`
}

type Config struct {
	PwdConfig struct {
		PwdMinLen       int  `json:"pwd_min_len" bson:"pwd_min_len"`             // 密码长度
		ForcePwdChange  bool `json:"force_pwd_change" bson:"force_pwd_change"`   //// 是否强制用户修改密码
		AllowSelfChange bool `json:"allow_self_change" bson:"allow_self_change"` // 是否允许自己修改密码
		PwdReqUpper     bool `json:"pwd_req_upper" bson:"pwd_req_upper"`         // 密码要求大写字母
		PwdReqLower     bool `json:"pwd_req_lower" bson:"pwd_req_lower"`         // 密码要求小写字母
		PwdReqDigits    bool `json:"pwd_req_digits" bson:"pwd_req_digits"`       // 密码要求数字
		PwdReqSpecial   bool `json:"pwd_req_special" bson:"pwd_req_special"`     // 密码要求特殊字符
	} `json:"pwd_config" bson:"pwd_config"`

	ThirdAuthSources []string `json:"third_auth_sources" bson:"third_auth_sources"`
}

type LdapConfig struct {
	LdapURL          string   `json:"ldap_url" bson:"ldap_url" `
	LdapUsername     string   `json:"ldap_username" bson:"ldap_username"`
	LdapPassword     string   `json:"ldap_password" bson:"ldap_password"`
	LdapDN           string   `json:"ldap_dn" bson:"ldap_dn"`
	IsInternal       bool     `json:"is_internal" bson:"is_internal"`
	ConnectorCluster []string `json:"connector_cluster" bson:"connector_cluster"`
	MirrorAddr       string   `json:"mirror_addr" bson:"mirror_addr"`
}

// OrgListRequest 组织列表请求
type OrgListRequest struct {
	ProductID string `json:"product_id"`
	SubDomain string `json:"sub_domain"`
}

type OrgListResponseData struct {
	Items     []OrgListResponse `json:"items"`
	LoginType string            `json:"login_type"`
}

// OrgListResponse 组织列表响应
type OrgListResponse struct {
	OrgName         string      `json:"org_name" bson:"org_name"`
	OrgCode         string      `json:"org_code" bson:"org_code"`
	SourceType      string      `json:"source_type" bson:"source_type"`
	AppKey          string      `json:"app_key" bson:"app_key"`
	ThirdAuthStatus bool        `json:"third_auth_status" bson:"third_auth_status"` // 是否快捷登录
	SourceConfig    *Config     `json:"source_config" bson:"source_config"`
	LdapConfig      *LdapConfig `json:"ldap_config" bson:"ldap_config"`
	SSOConfig       *SSOConfig  `json:"sso_config" bson:"sso_config"`
}

type OrganizationUser struct {
	ProductID             string     `bson:"product_id"` // 产品ID
	OrgCode               string     `bson:"org_code"`
	UserCode              string     `bson:"user_code"`
	UserName              string     `bson:"user_name"`
	Account               string     `bson:"account"`
	SourceType            string     `bson:"source_type"`
	SourceCode            string     `bson:"source_code"`
	Mobile                string     `bson:"mobile"`
	Email                 string     `bson:"email"`
	Position              string     `bson:"position"`
	Password              string     `bson:"password"`
	ValidTime             int64      `bson:"valid_time"`
	IsActivationEmailSent bool       `bson:"is_activation_email_sent"`
	Status                int64      `bson:"status"`             // 状态 0: 正常 1: 暂停使用 2: 锁定 3：已过期
	BindDeviceStatus      int64      `bson:"bind_device_status"` // 绑定设备状态 0-尚未已注册，1-已注册
	CreatedAt             int64      `bson:"created_at"`
	UpdatedAt             int64      `bson:"updated_at"`
	DeptRoles             []string   `bson:"dept_roles"` // 部门角色列表
	UserRoles             []string   `bson:"user_roles"` // 用户角色列表
	DeptPath              [][]string `bson:"dept_path"`  // 部门路径
	IsActive              bool       `bson:"is_active"`  // 是否激活
}

type EmployeeResponse struct {
	Success  bool `json:"success"`
	Employee struct {
		SourceCode string `json:"source_code"`
		Name       string `json:"name"`
		Account    string `json:"account"`
		Dn         string `json:"dn"`
	} `json:"employee"`
}

type OrgPlatformConfig struct {
	ID                string      `bson:"_id,omitempty" json:"-"`
	OrgCode           string      `bson:"org_code" json:"org_code"`
	OrgName           string      `bson:"org_name" json:"org_name"`
	SourceType        SourceType  `bson:"source_type" json:"source_type"`
	ProductID         string      `bson:"product_id" json:"product_id"`
	SyncStatus        SyncStatus  `bson:"sync_status" json:"sync_status"`
	LastSyncTime      int64       `bson:"last_sync_time" json:"last_sync_time"`
	LastUpdateBatchNo string      `bson:"last_update_batch_no" json:"last_update_batch_no"`
	Status            int         `bson:"status" json:"status"`
	CreatedAt         int64       `bson:"created_at" json:"-"`
	UpdatedAt         int64       `bson:"updated_at" json:"-"`
	AutoSync          int64       `json:"auto_sync" bson:"auto_sync"`
	CorpID            string      `json:"corp_id" bson:"corp_id"`
	AppKey            string      `json:"app_key" bson:"app_key"`
	Secret            string      `json:"secret" bson:"secret"`
	RangeType         int64       `json:"range_type" bson:"range_type"`         // 0:第三方的数据源 私有化数据源 1:自定义数据
	AutoSyncTime      int64       `json:"auto_sync_time" bson:"auto_sync_time"` // 使用秒的数据进行标记
	SyncMode          string      `json:"sync_mode" bson:"sync_mode"`
	CreateBy          string      `json:"create_by" bson:"create_by"`
	UpdateBy          string      `json:"update_by" bson:"update_by"`
	ThirdAuthStatus   bool        `json:"third_auth_status" bson:"third_auth_status"` // 快捷登录的开关
	LdapConfig        *LdapConfig `json:"ldap_config" bson:"ldap_config"`
}

type SourceType string

const (
	DINGTALK SourceType = "DINGTALK" // 钉钉
	WECHAT   SourceType = "WECOM"    // 企业微信
	FEISHU   SourceType = "FEISHU"   // 飞书
	CUSTOM   SourceType = "CUSTOM"   // 自定义组织
	LDAP     SourceType = "LDAP"

	GUEST SourceType = "GUEST" // 游客登陆的数据
)

// SyncStatus 定义同步状态枚举
type SyncStatus int64

const (
	NOTSYNCED           SyncStatus = 0 // 未同步
	SYNCING             SyncStatus = 1 // 同步中
	SYNCED              SyncStatus = 2 // 已同步
	FAILED              SyncStatus = 3 // 同步失败
	RangeTypeThirdParty            = 0
	RangeTypeCustom                = 1
	RangeTypeGuest                 = 2
)

type Department struct {
	ProductID      string `bson:"product_id" json:"product_id"`
	DeptCode       string `bson:"dept_code" json:"dept_code"`
	DeptName       string `bson:"dept_name" json:"dept_name"`
	OrgCode        string `bson:"org_code" json:"org_code"`
	ParentDeptCode string `bson:"parent_dept_code" json:"parent_dept_code"`
	CreatedAt      int64  `bson:"created_at" json:"created_at"`
	UpdatedAt      int64  `bson:"updated_at" json:"updated_at"`
	HasSubDept     bool   `bson:"has_sub_dept" json:"has_sub_dept"`
	Status         int32  `bson:"status" json:"status"`
	ImportBatchNo  string `bson:"import_batch_no" json:"import_batch_no"`
}
